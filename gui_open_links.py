#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动打开绑卡链接 GUI 版本
使用 PyQt5 创建图形界面，可以设置打开链接数量和延时
"""

import sys
import webbrowser
import time
import os
import threading
import subprocess
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QPushButton, QLabel, QLineEdit,
                             QTextEdit, QMessageBox, QGroupBox, QProgressBar,
                             QSplitter)
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QProcess, QTimer
from PyQt5.QtGui import QFont

class LinkOpenerThread(QThread):
    """
    在后台线程中打开链接，避免界面卡顿
    """
    progress_updated = pyqtSignal(int)  # 进度更新信号
    log_updated = pyqtSignal(str)       # 日志更新信号
    finished_signal = pyqtSignal()      # 完成信号
    
    def __init__(self, links_data, num_to_open, delay):
        super().__init__()
        self.links_data = links_data
        self.num_to_open = num_to_open
        self.delay = delay
        self.is_running = True
        self.processed_file = "已处理.txt"
    
    def run(self):
        """
        在后台线程中执行打开链接的操作
        """
        if not self.links_data:
            self.log_updated.emit("没有可用的链接数据！")
            self.finished_signal.emit()
            return
        
        # 确保不超过可用链接数量
        actual_num = min(self.num_to_open, len(self.links_data))
        
        self.log_updated.emit(f"准备打开 {actual_num} 个链接...")
        self.log_updated.emit("-" * 50)
        
        for i in range(actual_num):
            if not self.is_running:  # 检查是否被停止
                break
                
            email, link = self.links_data[i]
            self.log_updated.emit(f"正在打开第 {i+1} 个链接...")
            self.log_updated.emit(f"邮箱: {email}")
            self.log_updated.emit(f"链接: {link[:50]}..." if len(link) > 50 else f"链接: {link}")
            
            try:
                # 在默认浏览器中打开链接
                webbrowser.open(link)
                self.log_updated.emit(f"✓ 成功打开第 {i+1} 个链接")

                # 将已打开的链接记录到已处理文件中
                self.save_processed_link(email, link)

            except Exception as e:
                self.log_updated.emit(f"✗ 打开第 {i+1} 个链接失败：{e}")
            
            # 更新进度
            progress = int((i + 1) / actual_num * 100)
            self.progress_updated.emit(progress)
            
            # 在打开下一个链接前稍作延迟
            if i < actual_num - 1 and self.is_running:
                self.log_updated.emit(f"等待 {self.delay} 秒后打开下一个链接...")
                # 分割延迟时间，以便能够及时响应停止信号
                delay_steps = int(self.delay * 10)  # 每0.1秒检查一次
                for _ in range(delay_steps):
                    if not self.is_running:
                        break
                    time.sleep(0.1)
            
            self.log_updated.emit("-" * 30)
        
        if self.is_running:
            self.log_updated.emit(f"完成！共打开了 {actual_num} 个链接")
        else:
            self.log_updated.emit("操作已被用户停止")
        
        self.finished_signal.emit()
    
    def save_processed_link(self, email, link):
        """
        将已处理的链接保存到文件中
        """
        try:
            with open(self.processed_file, 'a', encoding='utf-8') as f:
                f.write(f"{email}----{link}\n")
        except Exception as e:
            self.log_updated.emit(f"保存已处理链接时出错：{e}")

    def stop(self):
        """
        停止线程执行
        """
        self.is_running = False

class IPSwitchThread(QThread):
    """
    在后台线程中执行IP切换操作
    """
    log_updated = pyqtSignal(str)       # 日志更新信号
    finished_signal = pyqtSignal(bool)  # 完成信号，参数表示是否成功

    def __init__(self):
        super().__init__()
        self.is_running = True
        self.process = None

    def run(self):
        """
        在后台线程中执行IP切换
        """
        try:
            self.log_updated.emit("开始执行IP切换...")

            # 获取当前脚本所在目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            ip_script_path = os.path.join(current_dir, "ip全自动切换.py")
            test_script_path = os.path.join(current_dir, "test_ip_output.py")

            # 优先使用测试脚本进行调试
            if os.path.exists(test_script_path):
                script_to_run = test_script_path
                self.log_updated.emit("使用测试脚本进行调试...")
            elif os.path.exists(ip_script_path):
                script_to_run = ip_script_path
                self.log_updated.emit("使用正式IP切换脚本...")
            else:
                self.log_updated.emit(f"错误：找不到IP切换脚本")
                self.log_updated.emit(f"查找路径1: {ip_script_path}")
                self.log_updated.emit(f"查找路径2: {test_script_path}")
                self.finished_signal.emit(False)
                return

            self.log_updated.emit(f"✓ 准备启动脚本: {os.path.basename(script_to_run)}")

            # 使用 Popen 实现实时输出
            try:
                self.log_updated.emit("--- 脚本输出开始 ---")

                # 创建进程，实时捕获输出
                self.process = subprocess.Popen(
                    [sys.executable, '-u', script_to_run],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,  # 将错误输出重定向到标准输出
                    text=True,
                    encoding='utf-8',
                    bufsize=1,  # 行缓冲
                    universal_newlines=True,
                    env={**os.environ, 'PYTHONUNBUFFERED': '1'}
                )

                # 实时读取输出
                start_time = time.time()
                timeout = 300  # 5分钟超时

                while self.is_running:
                    # 检查超时
                    if time.time() - start_time > timeout:
                        self.log_updated.emit("✗ 脚本执行超时（5分钟）")
                        self.process.terminate()
                        self.finished_signal.emit(False)
                        return

                    # 检查进程是否结束
                    if self.process.poll() is not None:
                        break

                    # 读取一行输出
                    try:
                        line = self.process.stdout.readline()
                        if line:
                            line = line.strip()
                            if line:  # 只显示非空行
                                self.log_updated.emit(line)
                        else:
                            time.sleep(0.1)  # 避免CPU占用过高
                    except Exception as e:
                        self.log_updated.emit(f"读取输出时出错: {e}")
                        break

                # 读取剩余的输出
                if self.is_running and self.process.poll() is not None:
                    remaining_output = self.process.stdout.read()
                    if remaining_output:
                        for line in remaining_output.strip().split('\n'):
                            if line.strip():
                                self.log_updated.emit(line.strip())

                self.log_updated.emit("--- 脚本输出结束 ---")

                # 检查执行结果
                if self.is_running:
                    return_code = self.process.returncode
                    self.log_updated.emit(f"脚本执行完成，退出码: {return_code}")
                    if return_code == 0:
                        self.log_updated.emit("✓ IP切换完成")
                        self.finished_signal.emit(True)
                    else:
                        self.log_updated.emit(f"✗ IP切换失败，退出码: {return_code}")
                        self.finished_signal.emit(False)
                else:
                    # 用户停止了操作
                    if hasattr(self, 'process') and self.process.poll() is None:
                        self.process.terminate()
                        try:
                            self.process.wait(timeout=5)
                        except subprocess.TimeoutExpired:
                            self.process.kill()
                    self.log_updated.emit("IP切换操作被用户停止")
                    self.finished_signal.emit(False)

            except Exception as e:
                self.log_updated.emit(f"✗ 执行脚本时出错：{e}")
                self.finished_signal.emit(False)

        except Exception as e:
            self.log_updated.emit(f"执行IP切换时出错：{e}")
            self.finished_signal.emit(False)

    def stop(self):
        """
        停止IP切换操作
        """
        self.is_running = False
        self.log_updated.emit("正在停止IP切换操作...")

        # 如果进程还在运行，尝试终止它
        if hasattr(self, 'process') and self.process and self.process.poll() is None:
            try:
                self.process.terminate()
                # 等待进程结束，最多等待5秒
                try:
                    self.process.wait(timeout=5)
                    self.log_updated.emit("✓ 进程已正常终止")
                except subprocess.TimeoutExpired:
                    # 如果5秒后还没结束，强制杀死进程
                    self.process.kill()
                    self.process.wait()
                    self.log_updated.emit("✓ 进程已强制终止")
            except Exception as e:
                self.log_updated.emit(f"终止进程时出错: {e}")

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.all_links_data = []  # 存储所有原始链接
        self.worker_thread = None
        self.ip_switch_thread = None  # IP切换线程
        self.processed_file = "已处理.txt"
        self.init_ui()
        self.load_all_links_data()
    
    def init_ui(self):
        """
        初始化用户界面
        """
        self.setWindowTitle("自动打开绑卡链接工具")
        self.setGeometry(100, 100, 600, 500)

        # 设置窗口置顶
        self.setWindowFlags(self.windowFlags() | Qt.WindowStaysOnTopHint)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 设置参数区域
        settings_group = QGroupBox("设置参数")
        settings_layout = QVBoxLayout(settings_group)
        
        # 链接数量设置
        links_layout = QHBoxLayout()
        links_label = QLabel("打开链接数量:")
        links_label.setFont(QFont("Arial", 12))
        self.links_input = QLineEdit()
        self.links_input.setText("2")
        self.links_input.setFont(QFont("Arial", 14))
        self.links_input.setMinimumHeight(35)
        self.links_input.setMaximumWidth(100)
        self.links_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #ddd;
                border-radius: 5px;
                padding: 5px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
            }
        """)
        links_layout.addWidget(links_label)
        links_layout.addWidget(self.links_input)
        links_layout.addStretch()

        # 延时设置
        delay_layout = QHBoxLayout()
        delay_label = QLabel("链接间延时(秒):")
        delay_label.setFont(QFont("Arial", 12))
        self.delay_input = QLineEdit()
        self.delay_input.setText("1.0")
        self.delay_input.setFont(QFont("Arial", 14))
        self.delay_input.setMinimumHeight(35)
        self.delay_input.setMaximumWidth(100)
        self.delay_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #ddd;
                border-radius: 5px;
                padding: 5px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
            }
        """)
        delay_layout.addWidget(delay_label)
        delay_layout.addWidget(self.delay_input)
        delay_layout.addStretch()
        
        settings_layout.addLayout(links_layout)
        settings_layout.addLayout(delay_layout)
        
        # 快捷按钮区域
        quick_buttons_group = QGroupBox("快捷操作")
        quick_buttons_layout = QHBoxLayout(quick_buttons_group)

        self.quick_7_button = QPushButton("打开 7 个链接")
        self.quick_7_button.setFont(QFont("Arial", 12, QFont.Bold))
        self.quick_7_button.setMinimumHeight(45)
        self.quick_7_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.quick_7_button.clicked.connect(lambda: self.start_opening_links_quick(7))

        self.quick_5_button = QPushButton("打开 5 个链接")
        self.quick_5_button.setFont(QFont("Arial", 12, QFont.Bold))
        self.quick_5_button.setMinimumHeight(45)
        self.quick_5_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:pressed {
                background-color: #E65100;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.quick_5_button.clicked.connect(lambda: self.start_opening_links_quick(5))

        self.close_browser_button = QPushButton("关闭所有浏览器")
        self.close_browser_button.setFont(QFont("Arial", 12, QFont.Bold))
        self.close_browser_button.setMinimumHeight(45)
        self.close_browser_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
            QPushButton:pressed {
                background-color: #c62828;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.close_browser_button.clicked.connect(self.close_all_browsers)

        # IP切换按钮
        self.switch_ip_button = QPushButton("自动换IP")
        self.switch_ip_button.setFont(QFont("Arial", 12, QFont.Bold))
        self.switch_ip_button.setMinimumHeight(45)
        self.switch_ip_button.setStyleSheet("""
            QPushButton {
                background-color: #9C27B0;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #7B1FA2;
            }
            QPushButton:pressed {
                background-color: #6A1B9A;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.switch_ip_button.clicked.connect(self.start_ip_switch)

        quick_buttons_layout.addWidget(self.quick_7_button)
        quick_buttons_layout.addWidget(self.quick_5_button)
        quick_buttons_layout.addWidget(self.close_browser_button)
        quick_buttons_layout.addWidget(self.switch_ip_button)

        # 控制按钮
        button_layout = QHBoxLayout()
        self.open_button = QPushButton("开始打开链接")
        self.open_button.setFont(QFont("Arial", 14, QFont.Bold))
        self.open_button.setMinimumHeight(50)
        self.open_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.open_button.clicked.connect(self.start_opening_links)
        
        self.stop_button = QPushButton("停止")
        self.stop_button.setFont(QFont("Arial", 12))
        self.stop_button.setMinimumHeight(50)
        self.stop_button.setEnabled(False)
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:pressed {
                background-color: #c1170a;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.stop_button.clicked.connect(self.stop_opening_links)
        
        button_layout.addWidget(self.open_button)
        button_layout.addWidget(self.stop_button)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        # 状态信息区域
        status_group = QGroupBox("状态信息")
        status_layout = QVBoxLayout(status_group)

        self.status_label = QLabel("就绪")
        self.status_label.setFont(QFont("Arial", 10))

        # 日志显示区域 - 分为左右两部分
        log_splitter = QSplitter(Qt.Horizontal)

        # 左侧：链接操作日志
        left_log_widget = QWidget()
        left_log_layout = QVBoxLayout(left_log_widget)
        left_log_layout.setContentsMargins(0, 0, 0, 0)

        left_log_label = QLabel("链接操作日志")
        left_log_label.setFont(QFont("Arial", 10, QFont.Bold))
        left_log_label.setStyleSheet("color: #2196F3; margin-bottom: 5px;")

        self.log_text = QTextEdit()
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setReadOnly(True)

        left_log_layout.addWidget(left_log_label)
        left_log_layout.addWidget(self.log_text)

        # 右侧：IP切换日志
        right_log_widget = QWidget()
        right_log_layout = QVBoxLayout(right_log_widget)
        right_log_layout.setContentsMargins(0, 0, 0, 0)

        right_log_label = QLabel("IP切换日志")
        right_log_label.setFont(QFont("Arial", 10, QFont.Bold))
        right_log_label.setStyleSheet("color: #9C27B0; margin-bottom: 5px;")

        self.ip_log_text = QTextEdit()
        self.ip_log_text.setFont(QFont("Consolas", 9))
        self.ip_log_text.setReadOnly(True)
        self.ip_log_text.setPlaceholderText("点击'自动换IP'按钮开始IP切换...")

        right_log_layout.addWidget(right_log_label)
        right_log_layout.addWidget(self.ip_log_text)

        # 添加到分割器
        log_splitter.addWidget(left_log_widget)
        log_splitter.addWidget(right_log_widget)
        log_splitter.setSizes([300, 300])  # 设置初始大小比例
        log_splitter.setMaximumHeight(200)

        status_layout.addWidget(self.status_label)
        status_layout.addWidget(log_splitter)
        
        # 添加所有组件到主布局
        main_layout.addWidget(settings_group)
        main_layout.addWidget(quick_buttons_group)
        main_layout.addLayout(button_layout)
        main_layout.addWidget(self.progress_bar)
        main_layout.addWidget(status_group)
        
        # 初始化状态
        self.update_status()

    def load_processed_links(self):
        """
        加载已处理的链接，返回已处理链接的集合
        """
        processed_links = set()

        if not os.path.exists(self.processed_file):
            return processed_links

        try:
            with open(self.processed_file, 'r', encoding='utf-8') as file:
                for line in file:
                    line = line.strip()
                    if line and '----' in line:
                        parts = line.split('----', 1)
                        if len(parts) == 2:
                            email = parts[0].strip()
                            link = parts[1].strip()
                            if email and link:
                                # 使用邮箱+链接作为唯一标识
                                processed_links.add(f"{email}----{link}")

        except Exception as e:
            print(f"读取已处理文件时出错：{e}")

        return processed_links

    def get_available_links(self):
        """
        获取可用的（未处理的）链接
        """
        processed_links = self.load_processed_links()
        available_links = []

        for email, link in self.all_links_data:
            link_key = f"{email}----{link}"
            if link_key not in processed_links:
                available_links.append((email, link))

        return available_links, len(processed_links)

    def load_all_links_data(self):
        """
        加载所有原始链接数据
        """
        data_file = "绑卡.txt"
        self.all_links_data = []

        if not os.path.exists(data_file):
            self.log_text.append(f"错误：文件 {data_file} 不存在！")
            return

        try:
            with open(data_file, 'r', encoding='utf-8') as file:
                for line in file:
                    line = line.strip()
                    if not line:  # 跳过空行
                        continue

                    if '----' in line:
                        parts = line.split('----', 1)
                        if len(parts) == 2:
                            email = parts[0].strip()
                            link = parts[1].strip()
                            if email and link:
                                self.all_links_data.append((email, link))

            self.log_text.append(f"成功加载 {len(self.all_links_data)} 个原始链接")
            self.update_status()

        except Exception as e:
            self.log_text.append(f"读取文件时出错：{e}")
    
    def update_status(self):
        """
        更新状态信息
        """
        if self.all_links_data:
            available_links, processed_count = self.get_available_links()
            available_count = len(available_links)
            total_count = len(self.all_links_data)
            self.status_label.setText(f"总链接: {total_count} | 可用: {available_count} | 已处理: {processed_count} | 就绪")
        else:
            self.status_label.setText("未找到有效链接数据")
    
    def start_opening_links(self):
        """
        开始打开链接（使用输入框的数量）
        """
        # 获取输入值并验证
        try:
            num_to_open = int(self.links_input.text())
            if num_to_open < 1:
                raise ValueError("链接数量必须大于0")
        except ValueError:
            QMessageBox.warning(self, "输入错误", "请输入有效的链接数量（正整数）")
            return

        try:
            delay = float(self.delay_input.text())
            if delay < 0:
                raise ValueError("延时必须大于等于0")
        except ValueError:
            QMessageBox.warning(self, "输入错误", "请输入有效的延时时间（数字）")
            return

        self.start_opening_links_with_params(num_to_open, delay)

    def start_opening_links_quick(self, num_links):
        """
        快捷按钮：直接打开指定数量的链接
        """
        # 使用默认延时
        try:
            delay = float(self.delay_input.text())
            if delay < 0:
                delay = 1.0  # 如果延时输入无效，使用默认值1秒
        except ValueError:
            delay = 1.0  # 如果延时输入无效，使用默认值1秒

        self.start_opening_links_with_params(num_links, delay)

    def start_opening_links_with_params(self, num_to_open, delay):
        """
        使用指定参数打开链接的统一方法
        """
        # 获取当前可用的链接
        available_links, processed_count = self.get_available_links()

        if not available_links:
            QMessageBox.warning(self, "警告", "没有可用的链接数据！\n所有链接可能都已处理完毕。")
            return

        # 禁用所有按钮
        self.open_button.setEnabled(False)
        self.quick_7_button.setEnabled(False)
        self.quick_5_button.setEnabled(False)
        self.stop_button.setEnabled(True)

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 清空日志
        self.log_text.clear()

        # 显示当前状态
        total_count = len(self.all_links_data)
        available_count = len(available_links)
        self.log_text.append(f"总链接数: {total_count}")
        self.log_text.append(f"已处理: {processed_count}")
        self.log_text.append(f"可用链接: {available_count}")
        self.log_text.append(f"本次将打开: {min(num_to_open, available_count)} 个链接")
        self.log_text.append("-" * 50)

        # 创建并启动工作线程
        self.worker_thread = LinkOpenerThread(available_links, num_to_open, delay)
        self.worker_thread.progress_updated.connect(self.progress_bar.setValue)
        self.worker_thread.log_updated.connect(self.log_text.append)
        self.worker_thread.finished_signal.connect(self.on_finished)
        self.worker_thread.start()

    def close_all_browsers(self):
        """
        关闭所有浏览器窗口
        """
        try:
            # 关闭Chrome浏览器的所有进程
            subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'],
                         capture_output=True, text=True, check=False)

            # 也尝试关闭其他常见浏览器
            browsers = ['msedge.exe', 'firefox.exe', 'opera.exe', 'brave.exe']
            for browser in browsers:
                subprocess.run(['taskkill', '/f', '/im', browser],
                             capture_output=True, text=True, check=False)

            self.log_text.append("✓ 已尝试关闭所有浏览器窗口")

        except Exception as e:
            self.log_text.append(f"✗ 关闭浏览器时出错：{e}")

    def stop_opening_links(self):
        """
        停止打开链接
        """
        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.stop()
            self.log_text.append("正在停止操作...")
    
    def on_finished(self):
        """
        操作完成后的处理
        """
        # 恢复按钮状态
        self.open_button.setEnabled(True)
        self.quick_7_button.setEnabled(True)
        self.quick_5_button.setEnabled(True)
        self.stop_button.setEnabled(False)

        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 更新状态信息
        self.update_status()

        # 清理线程
        if self.worker_thread:
            self.worker_thread.quit()
            self.worker_thread.wait()
            self.worker_thread = None

    def start_ip_switch(self):
        """
        开始IP切换操作
        """
        # 检查是否已有IP切换操作在进行
        if self.ip_switch_thread and self.ip_switch_thread.isRunning():
            QMessageBox.warning(self, "警告", "IP切换操作正在进行中，请等待完成！")
            return

        # 禁用IP切换按钮
        self.switch_ip_button.setEnabled(False)

        # 清空IP日志
        self.ip_log_text.clear()

        # 创建并启动IP切换线程
        self.ip_switch_thread = IPSwitchThread()
        self.ip_switch_thread.log_updated.connect(self.ip_log_text.append)
        self.ip_switch_thread.finished_signal.connect(self.on_ip_switch_finished)
        self.ip_switch_thread.start()

        self.ip_log_text.append("开始IP切换操作...")

    def on_ip_switch_finished(self, success):
        """
        IP切换完成后的处理
        """
        # 恢复IP切换按钮
        self.switch_ip_button.setEnabled(True)

        if success:
            self.ip_log_text.append("🎉 IP切换操作成功完成！")
        else:
            self.ip_log_text.append("❌ IP切换操作失败或被中断")

        # 清理线程
        if self.ip_switch_thread:
            self.ip_switch_thread.quit()
            self.ip_switch_thread.wait()
            self.ip_switch_thread = None

def main():
    """
    主函数
    """
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("自动打开绑卡链接工具")
    app.setApplicationVersion("1.0")
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
