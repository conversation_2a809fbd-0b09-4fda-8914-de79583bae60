#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试IP切换脚本输出的简单脚本
"""
import time
import sys
from datetime import datetime

def log(msg: str, level="INFO"):
    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
    prefix = f"[{timestamp}] [{level}]"
    print(f"{prefix} {msg}")
    sys.stdout.flush()  # 强制刷新输出缓冲区

def main():
    log("启动IP切换脚本测试")
    log("当前API: PQS.PW")
    log("代理名称: pqs555")
    
    time.sleep(1)
    log("环境变量代理检测: 无")
    
    time.sleep(1)
    log("开始获取当前IP...")
    log("当前 IP: **************")
    
    time.sleep(1)
    log("关闭代理 (Clash→DIRECT)…")
    log("Clash 模式已切换为 DIRECT")
    
    time.sleep(2)
    log("准备调用IP切换接口...")
    log("第 1/10 次尝试切换IP...")
    log("调用切换IP接口 - 预计等待时间: 最长 180s")
    
    time.sleep(3)
    log("切换IP接口返回成功 - 耗时: 2.34s - 返回IP: **************")
    log("✓ IP切换成功: ************** → **************")
    
    time.sleep(1)
    log("开始快速替换代理IP为: **************")
    log("✓ 快速IP替换成功，Clash配置已重载")
    
    time.sleep(1)
    log("🚀 快速IP替换成功，跳过传统轮询验证")
    log("清理 Clash 连接...")
    log("已清连接 & 刷新 DNS")
    
    time.sleep(1)
    log("✓ IP切换流程完成")
    log("等0秒后开始最终验证")
    log("开始最终验证步骤...")
    
    time.sleep(2)
    log("✓ 代理验证成功: 通过代理获取的IP (**************) 已不同于原IP (**************)")
    log("🎉 完整的IP切换流程成功完成！代理模式下IP已更新")
    
    time.sleep(1)
    log("=" * 50)
    log("诊断报告")
    log("=" * 50)
    log("总日志条数: 25")
    log("错误日志: 0")
    log("警告日志: 0")
    log("总耗时: 15.2s")
    log("=" * 50)

if __name__ == "__main__":
    main()
